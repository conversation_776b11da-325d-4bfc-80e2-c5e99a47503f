import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/image_picker_service.dart';
import '../../../../core/utils/permission_helper.dart';
import '../../../../core/services/ai_generation_service.dart';
import '../../../../core/services/environment_service.dart';
import '../../../../core/services/image_save_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/wedding_colors.dart';
import '../../../../core/theme/design_system/wedding_buttons.dart';
import '../../../settings/presentation/screens/api_config_screen.dart';
import 'generation_result_screen.dart';

@RoutePage()
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  List<File> _uploadedImages = [];
  bool _isGenerating = false;
  final AIGenerationService _aiService = AIGenerationService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Hera',
          style: TextStyle(
            fontFamily: 'Dancing Script',
            fontSize: 28.sp,
            fontWeight: FontWeight.w400,
            color: WeddingColors.primaryPink,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // API配置按钮（开发模式）
          if (!ApiConfig.hasApiKey || !EnvironmentService.validateApiKey(ApiConfig.apiKey))
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: _openApiConfig,
              tooltip: 'API配置',
            ),
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () => AppTheme.toggleThemeMode(ref),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎区域
            _buildWelcomeSection(),
            
            SizedBox(height: 32.h),
            
            // 图片上传功能
            _buildImageUploadSection(),
            
            SizedBox(height: 32.h),
            
            // 操作按钮
            if (_uploadedImages.isNotEmpty) _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建欢迎区域
  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: WeddingColors.weddingGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: WeddingColors.shadowMedium,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 装饰性心形图标
          Row(
            children: [
              Icon(
                Icons.favorite,
                color: WeddingColors.pureWhite.withOpacity(0.8),
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                '欢迎使用',
                style: TextStyle(
                  fontFamily: 'SF Pro Display',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: WeddingColors.pureWhite.withOpacity(0.9),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'Hera AI',
            style: TextStyle(
              fontFamily: 'Dancing Script',
              fontSize: 32.sp,
              fontWeight: FontWeight.w400,
              color: WeddingColors.pureWhite,
              height: 1.2,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            '上传您的照片，让AI为您生成专属婚纱照',
            style: TextStyle(
              fontFamily: 'SF Pro Display',
              fontSize: 16.sp,
              color: WeddingColors.pureWhite.withOpacity(0.9),
              height: 1.5,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 16.h),
          // 装饰性分割线
          Container(
            width: 60.w,
            height: 2.h,
            decoration: BoxDecoration(
              color: WeddingColors.accentGold.withOpacity(0.8),
              borderRadius: BorderRadius.circular(1.r),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片上传区域
  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '上传照片',
          style: TextStyle(
            fontFamily: 'Playfair Display',
            fontSize: 22.sp,
            fontWeight: FontWeight.w600,
            color: WeddingColors.darkGray,
            height: 1.3,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          '请上传清晰的正面照片，最多3张，支持JPG、PNG格式',
          style: TextStyle(
            fontFamily: 'SF Pro Display',
            fontSize: 14.sp,
            color: WeddingColors.mediumGray,
            height: 1.4,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(height: 16.h),
        
        // 图片选择按钮
        _buildImagePickerButtons(),
        
        SizedBox(height: 16.h),
        
        // 已选择图片展示
        if (_uploadedImages.isNotEmpty) _buildSelectedImages(),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 生成按钮
        WeddingButtons.primary(
          text: _isGenerating ? 'AI正在生成中...' : '开始生成婚纱照',
          onPressed: _isGenerating ? () {} : _generateWeddingPhotos,
          width: double.infinity,
          isLoading: _isGenerating,
          isEnabled: !_isGenerating,
        ),

        SizedBox(height: 12.h),

        // 权限设置按钮
        WeddingButtons.secondary(
          text: '设置相册权限',
          onPressed: _requestPermissions,
          width: double.infinity,
        ),

        SizedBox(height: 12.h),

        // 保存原图按钮
        WeddingButtons.outline(
          text: '保存原图到相册',
          onPressed: _uploadedImages.isNotEmpty ? _saveOriginalImages : () {},
          width: double.infinity,
          isEnabled: _uploadedImages.isNotEmpty,
        ),

        SizedBox(height: 12.h),

        // 清空按钮
        WeddingButtons.outline(
          text: '清空图片',
          onPressed: _clearImages,
          width: double.infinity,
          borderColor: WeddingColors.mediumGray,
          textColor: WeddingColors.mediumGray,
        ),
      ],
    );
  }

  /// 构建图片选择按钮
  Widget _buildImagePickerButtons() {
    return Row(
      children: [
        // 拍照按钮
        Expanded(
          child: Container(
            height: 50.h,
            child: ElevatedButton.icon(
              onPressed: _uploadedImages.length < 3 ? _pickImageFromCamera : null,
              icon: Icon(Icons.camera_alt, size: 20.sp),
              label: Text(
                '拍照',
                style: TextStyle(fontSize: 14.sp),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
            ),
          ),
        ),
        
        SizedBox(width: 12.w),
        
        // 从相册选择按钮
        Expanded(
          child: Container(
            height: 50.h,
            child: ElevatedButton.icon(
              onPressed: _uploadedImages.length < 3 ? _pickImageFromGallery : null,
              icon: Icon(Icons.photo_library, size: 20.sp),
              label: Text(
                '相册',
                style: TextStyle(fontSize: 14.sp),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
            ),
          ),
        ),
        
        SizedBox(width: 12.w),
        
        // 选择多张按钮
        Expanded(
          child: Container(
            height: 50.h,
            child: ElevatedButton.icon(
              onPressed: _uploadedImages.length < 3 ? _pickMultipleImages : null,
              icon: Icon(Icons.photo_library_outlined, size: 20.sp),
              label: Text(
                '多选',
                style: TextStyle(fontSize: 14.sp),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建已选择图片展示
  Widget _buildSelectedImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '已选择图片 (${_uploadedImages.length}/3)',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            if (_uploadedImages.isNotEmpty)
              TextButton.icon(
                onPressed: _clearImages,
                icon: Icon(Icons.clear_all, size: 16.sp, color: Colors.red),
                label: Text(
                  '清空',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: 12.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.h,
            childAspectRatio: 1,
          ),
          itemCount: _uploadedImages.length,
          itemBuilder: (context, index) => _buildImageItem(_uploadedImages[index], index),
        ),
      ],
    );
  }

  /// 构建图片项
  Widget _buildImageItem(File image, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.file(
              image,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 16.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera() async {
    final image = await ImagePickerService.pickImageFromCamera(context);
    if (image != null) {
      _addImage(image);
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    final image = await ImagePickerService.pickImageFromGallery(context);
    if (image != null) {
      _addImage(image);
    }
  }

  /// 选择多张图片
  Future<void> _pickMultipleImages() async {
    final remainingSlots = 3 - _uploadedImages.length;
    final images = await ImagePickerService.pickMultipleImages(
      context,
      limit: remainingSlots,
    );
    for (final image in images) {
      _addImage(image);
    }
  }

  /// 添加图片
  void _addImage(File image) {
    if (_uploadedImages.length < 3) {
      setState(() {
        _uploadedImages.add(image);
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已添加图片，当前 ${_uploadedImages.length}/3 张'),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('最多只能选择3张图片'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _uploadedImages.removeAt(index);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已移除图片，当前 ${_uploadedImages.length}/3 张'),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 打开API配置页面
  Future<void> _openApiConfig() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ApiConfigScreen(),
      ),
    );

    if (result == true && mounted) {
      // 配置更新后刷新界面
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              const Text('API配置已更新'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// 生成婚纱照
  Future<void> _generateWeddingPhotos() async {
    if (_uploadedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先上传至少一张照片'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 检查API配置
    if (!ApiConfig.hasApiKey) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.warning, color: Colors.white),
              const SizedBox(width: 8),
              const Expanded(child: Text('请先配置API密钥')),
              TextButton(
                onPressed: _openApiConfig,
                child: const Text('去配置', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    if (!EnvironmentService.validateApiKey(ApiConfig.apiKey)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              const Expanded(child: Text('API密钥格式无效')),
              TextButton(
                onPressed: _openApiConfig,
                child: const Text('重新配置', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      // 显示生成开始提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('开始AI分析和生成婚纱照，共使用 ${_uploadedImages.length} 张照片...'),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 2),
        ),
      );

      // 调用AI生成服务
      final result = await _aiService.generateWeddingPhotos(_uploadedImages);

      setState(() {
        _isGenerating = false;
      });

      // 检查生成结果
      if (result.success) {
        // 导航到结果页面
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => GenerationResultScreen(result: result),
            ),
          );
        }
      } else {
        // 显示错误信息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text(result.message)),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isGenerating = false;
      });

      if (mounted) {
        String errorMessage = e.toString();
        Color backgroundColor = Colors.red;

        // 根据错误类型提供不同的处理建议
        if (errorMessage.contains('API密钥')) {
          backgroundColor = Colors.orange;
        } else if (errorMessage.contains('网络')) {
          backgroundColor = Colors.blue;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('生成失败: $errorMessage')),
                if (errorMessage.contains('API密钥'))
                  TextButton(
                    onPressed: _openApiConfig,
                    child: const Text('配置', style: TextStyle(color: Colors.white)),
                  ),
              ],
            ),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 5),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 请求权限
  Future<void> _requestPermissions() async {
    final hasPermission = await PermissionHelper.requestSaveImagePermission(context);
    
    if (hasPermission) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              const Text('相册权限已开启！可以正常保存图片了'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// 保存原图到相册
  Future<void> _saveOriginalImages() async {
    if (_uploadedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有可保存的图片'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // 显示保存开始提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('开始保存 ${_uploadedImages.length} 张图片到相册...'),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 2),
        ),
      );

      final result = await ImageSaveService.saveBatchImages(
        localFiles: _uploadedImages,
        context: context,
        onProgress: (current, total) {
          // 可以在这里显示进度
        },
      );

      // 批量保存结果在 ImageSaveService 中已经显示
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text('保存失败：$e')),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// 清空图片
  void _clearImages() {
    setState(() {
      _uploadedImages.clear();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已清空所有图片'),
        duration: Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}